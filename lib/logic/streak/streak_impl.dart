import 'package:unstack/logic/streak/streak_contract.dart';

class StreakManager implements IStreakManagerContract {
  @override
  Future<void> getCompletionHistory() {
    // TODO: implement getCompletionHistory
    throw UnimplementedError();
  }

  @override
  Future<void> getCurrentStreak() {
    // TODO: implement getCurrentStreak
    throw UnimplementedError();
  }

  @override
  Future<void> getLongestStreak() {
    // TODO: implement getLongestStreak
    throw UnimplementedError();
  }

  @override
  Future<void> getMonthCompletionPercentage() {
    // TODO: implement getMonthCompletionPercentage
    throw UnimplementedError();
  }

  @override
  Future<void> getTotalCompletedDays() {
    // TODO: implement getTotalCompletedDays
    throw UnimplementedError();
  }

  @override
  Future<void> updateTodayCompletion() {
    // TODO: implement updateTodayCompletion
    throw UnimplementedError();
  }

  @override
  Future<void> resetStreak() {
    // TODO: implement resetStreak
    throw UnimplementedError();
  }
}
