class SubscriptionModel {
  final String? id;

  final bool isPremium;
  final DateTime? subscriptionStartDate;
  final DateTime? subscriptionEndDate;

  SubscriptionModel({
    this.id,
    this.isPremium = false,
    this.subscriptionStartDate,
    this.subscriptionEndDate,
  });

  factory SubscriptionModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionModel(
      id: json['id'],
      isPremium: json['isPremium'] ?? false,
      subscriptionStartDate: json['subscriptionStartDate'] != null
          ? DateTime.parse(json['subscriptionStartDate'])
          : null,
      subscriptionEndDate: json['subscriptionEndDate'] != null
          ? DateTime.parse(json['subscriptionEndDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'isPremium': isPremium,
      'subscriptionStartDate': subscriptionStartDate?.toIso8601String(),
      'subscriptionEndDate': subscriptionEndDate?.toIso8601String(),
    };
  }
}
