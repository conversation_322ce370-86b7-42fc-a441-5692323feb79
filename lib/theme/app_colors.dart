import 'package:flutter/material.dart';

class AppColors {
  static const Color whiteColor = Colors.white;
  static const Color lightWhiteColor = Color.fromARGB(255, 209, 209, 209);

  static const Color blackColor = Colors.black;

  // Background colors
  static const Color backgroundPrimary = Color(0xFF121212);
  static const Color backgroundSecondary = Color(0xFF2A2A2A);
  static const Color backgroundTertiary = Color(0xFF363636);

  // Surface colors
  static const Color surfaceCard = Color(0xFF2D2D2D);
  static const Color surfaceElevated = Color(0xFF3A3A3A);
  static const Color surfaceOverlay = Color(0xFF404040);

  // Accent colors
  static const Color accentPurple = Color(0xFF8B5CF6);
  static const Color accentBlue = Color(0xFF3B82F6);
  static const Color accentGreen = Color(0xFF10B981);
  static const Color accentYellow = Color(0xFFF59E0B);
  static const Color accentPink = Color(0xFFEC4899);
  static const Color accentRed = Color(0xFFEF4444);
  static const Color redShade = Color(0xFFf84e56);
  static const Color accentOrange = Color(0xFFF97316);

  // Text colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFA3A3A3);
  static const Color textMuted = Color(0xFF737373);
  static const Color textInverse = Color(0xFF000000);

  // Status colors
  static const Color statusSuccess = Color(0xFF22C55E);
  static const Color statusWarning = Color(0xFFEAB308);
  static const Color statusError = Color(0xFFEF4444);
  static const Color statusInfo = Color(0xFF3B82F6);

  // Glassmorphism colors
  static const Color glassBackground = Color(0x1AFFFFFF);
  static const Color glassBorder = Color(0x33FFFFFF);
}
