import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:unstack/models/task.model.dart';
import 'package:unstack/theme/theme.dart';
import 'package:unstack/routes/route.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';

class TaskDetailsPage extends StatefulWidget {
  final String heroTag;
  final Task? task;

  const TaskDetailsPage({
    required this.heroTag,
    this.task,
    super.key,
  });

  @override
  State<TaskDetailsPage> createState() => _TaskDetailsPageState();
}

class _TaskDetailsPageState extends State<TaskDetailsPage>
    with TickerProviderStateMixin {
  late Task _currentTask;

  @override
  void initState() {
    super.initState();
    _currentTask = widget.task ??
        Task(
          id: 'temp',
          title: 'New Task',
          description: '',
          priority: TaskPriority.medium,
          createdAt: DateTime.now(),
        );
  }

  void _navigateToEditTask() async {
    final result = await RouteUtils.pushNamed(
      context,
      RoutePaths.addTaskPage,
      arguments: {
        'task': _currentTask,
        'fromTaskDetails': true,
        'edit': true,
      },
    );

    // Update task if changes were made
    if (result != null && result is Task) {
      setState(() {
        _currentTask = result;
      });
    }
  }

  void _deleteTask() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surfaceCard,
        title: Text(
          'Delete Task',
          style: AppTextStyles.h3.copyWith(color: AppColors.textPrimary),
        ),
        content: Text(
          'Are you sure you want to delete this task? This action cannot be undone.',
          style:
              AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to tasks list
              HapticFeedback.heavyImpact();
            },
            child: Text(
              'Delete',
              style: TextStyle(color: AppColors.statusError),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(AppSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: AppSpacing.xl),
                _buildTaskInfo(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        HomeAppBarButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: CupertinoIcons.back,
        ),
        Spacer(),
        HomeAppBarButton(
          onPressed: _deleteTask,
          icon: CupertinoIcons.delete,
        ),
        const SizedBox(
          width: AppSpacing.sm,
        ),
        HomeAppBarButton(
          onPressed: _navigateToEditTask,
          icon: CupertinoIcons.pencil,
        ),
      ],
    );
  }

  Widget _buildTaskInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Priority Badge
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.sm,
          ),
          decoration: BoxDecoration(
            color: _currentTask.priority.color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(AppBorderRadius.full),
            border: Border.all(
              color: _currentTask.priority.color.withValues(alpha: 0.5),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.flag,
                size: 16,
                color: _currentTask.priority.color,
              ),
              SizedBox(width: 4),
              Text(
                _currentTask.priority.label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: _currentTask.priority.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppSpacing.md),
        // Task Title
        Text(
          _currentTask.title,
          style: AppTextStyles.h1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w700,
            fontSize: 28,
          ),
        ),

        if (_currentTask.description.isNotEmpty) ...[
          SizedBox(height: AppSpacing.sm),
          Text(
            _currentTask.description,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              height: 1.5,
            ),
          ),
        ],
      ],
    );
  }
}
