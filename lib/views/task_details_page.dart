import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:unstack/models/task.model.dart';
import 'package:unstack/theme/theme.dart';
import 'package:unstack/routes/route.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';
import 'dart:math' as math;

class TaskDetailsPage extends StatefulWidget {
  final String heroTag;
  final Task? task;

  const TaskDetailsPage({
    required this.heroTag,
    this.task,
    super.key,
  });

  @override
  State<TaskDetailsPage> createState() => _TaskDetailsPageState();
}

class _TaskDetailsPageState extends State<TaskDetailsPage>
    with TickerProviderStateMixin {
  late Task _currentTask;

  // Animation controllers for the completion sequence
  late AnimationController _holdProgressController;
  late AnimationController _radialExpansionController;
  late AnimationController _burstController;

  // Animations
  late Animation<double> _holdProgressAnimation;
  late Animation<double> _radialExpansionAnimation;
  late Animation<double> _burstAnimation;

  // State variables
  bool _isHolding = false;
  bool _isCompleting = false;
  bool _isCompleted = false;

  // Hold duration in seconds
  static const double _holdDuration = 2.5;

  @override
  void initState() {
    super.initState();
    _currentTask = widget.task ??
        Task(
          id: 'temp',
          title: 'New Task',
          description: '',
          priority: TaskPriority.medium,
          createdAt: DateTime.now(),
        );

    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Hold progress animation (2.5 seconds)
    _holdProgressController = AnimationController(
      duration: Duration(milliseconds: (_holdDuration * 1000).round()),
      vsync: this,
    );

    // Radial expansion animation (1 second)
    _radialExpansionController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Burst animation (0.5 seconds)
    _burstController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Create animations with curves
    _holdProgressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _holdProgressController,
      curve: Curves.easeInOut,
    ));

    _radialExpansionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _radialExpansionController,
      curve: Curves.easeInOut,
    ));

    _burstAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _burstController,
      curve: Curves.easeOut,
    ));

    // Add listeners
    _holdProgressController.addStatusListener(_onHoldProgressStatusChanged);
    _radialExpansionController
        .addStatusListener(_onRadialExpansionStatusChanged);
    _burstController.addStatusListener(_onBurstStatusChanged);
  }

  @override
  void dispose() {
    _holdProgressController.dispose();
    _radialExpansionController.dispose();
    _burstController.dispose();
    super.dispose();
  }

  // Animation status listeners
  void _onHoldProgressStatusChanged(AnimationStatus status) {
    if (status == AnimationStatus.completed && _isHolding) {
      _startRadialExpansion();
    }
  }

  void _onRadialExpansionStatusChanged(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      _startBurstAndComplete();
    }
  }

  void _onBurstStatusChanged(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      _finishCompletion();
    }
  }

  // Completion sequence methods
  void _startHoldProgress() {
    if (_currentTask.isCompleted || _isCompleting) return;

    setState(() {
      _isHolding = true;
    });

    HapticFeedback.lightImpact();
    _holdProgressController.forward();
  }

  void _cancelHoldProgress() {
    if (!_isHolding || _isCompleting) return;

    setState(() {
      _isHolding = false;
    });

    _holdProgressController.reset();
  }

  void _startRadialExpansion() {
    setState(() {
      _isCompleting = true;
    });

    HapticFeedback.mediumImpact();
    _radialExpansionController.forward();
  }

  void _startBurstAndComplete() {
    HapticFeedback.heavyImpact();
    _burstController.forward();
  }

  void _finishCompletion() {
    // Mark task as completed
    final completedTask = _currentTask.copyWith(isCompleted: true);

    setState(() {
      _currentTask = completedTask;
      _isCompleted = true;
    });

    // Navigate back after a short delay
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        Navigator.of(context).pop(completedTask);
      }
    });
  }

  void _navigateToEditTask() async {
    final result = await RouteUtils.pushNamed(
      context,
      RoutePaths.addTaskPage,
      arguments: {
        'task': _currentTask,
        'fromTaskDetails': true,
        'edit': true,
      },
    );

    // Update task if changes were made
    if (result != null && result is Task) {
      setState(() {
        _currentTask = result;
      });
    }
  }

  void _deleteTask() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surfaceCard,
        title: Text(
          'Delete Task',
          style: AppTextStyles.h3.copyWith(color: AppColors.textPrimary),
        ),
        content: Text(
          'Are you sure you want to delete this task? This action cannot be undone.',
          style:
              AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to tasks list
              HapticFeedback.heavyImpact();
            },
            child: Text(
              'Delete',
              style: TextStyle(color: AppColors.statusError),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: Stack(
        children: [
          SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.all(AppSpacing.lg),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeader(),
                          SizedBox(height: AppSpacing.xl),
                          _buildTaskInfo(),
                          SizedBox(height: AppSpacing.xxxl),
                        ],
                      ),
                    ),
                  ),
                ),
                if (!_currentTask.isCompleted) _buildCompletionButton(),
              ],
            ),
          ),
          // Radial expansion overlay
          if (_isCompleting) _buildRadialExpansionOverlay(),
          // Burst overlay
          if (_isCompleted) _buildBurstOverlay(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        HomeAppBarButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: CupertinoIcons.back,
        ),
        Spacer(),
        HomeAppBarButton(
          onPressed: _deleteTask,
          color: AppColors.statusError,
          icon: CupertinoIcons.delete,
        ),
        const SizedBox(
          width: AppSpacing.sm,
        ),
        HomeAppBarButton(
          onPressed: _navigateToEditTask,
          icon: CupertinoIcons.pencil,
        ),
      ],
    );
  }

  Widget _buildTaskInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Priority Badge
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.sm,
          ),
          decoration: BoxDecoration(
            color: _currentTask.priority.color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(AppBorderRadius.full),
            border: Border.all(
              color: _currentTask.priority.color.withValues(alpha: 0.5),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.flag,
                size: 16,
                color: _currentTask.priority.color,
              ),
              SizedBox(width: 4),
              Text(
                _currentTask.priority.label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: _currentTask.priority.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppSpacing.md),
        // Task Title
        Text(
          _currentTask.title,
          style: AppTextStyles.h1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w700,
            fontSize: 28,
          ),
        ),

        if (_currentTask.description.isNotEmpty) ...[
          SizedBox(height: AppSpacing.sm),
          Text(
            _currentTask.description,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              height: 1.5,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCompletionButton() {
    return Container(
      padding: EdgeInsets.all(AppSpacing.lg),
      child: AnimatedBuilder(
        animation: _holdProgressAnimation,
        builder: (context, child) {
          return GestureDetector(
            onTapDown: (_) => _startHoldProgress(),
            onTapUp: (_) => _cancelHoldProgress(),
            onTapCancel: () => _cancelHoldProgress(),
            child: Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppBorderRadius.full),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.accentGreen,
                    AppColors.accentGreen.withValues(alpha: 0.8),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.accentGreen.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Progress fill
                  AnimatedBuilder(
                    animation: _holdProgressAnimation,
                    builder: (context, child) {
                      return Container(
                        width: double.infinity,
                        height: 60,
                        decoration: BoxDecoration(
                          borderRadius:
                              BorderRadius.circular(AppBorderRadius.full),
                          gradient: RadialGradient(
                            center: Alignment.center,
                            radius: _holdProgressAnimation.value * 2,
                            colors: [
                              AppColors.accentGreen.withValues(alpha: 0.8),
                              AppColors.accentGreen.withValues(alpha: 0.4),
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.7, 1.0],
                          ),
                        ),
                      );
                    },
                  ),
                  // Button content
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: AppColors.textInverse,
                          size: 24,
                        ),
                        SizedBox(width: AppSpacing.sm),
                        Text(
                          _isHolding
                              ? 'Hold to Complete...'
                              : 'Hold to Complete Task',
                          style: AppTextStyles.buttonLarge.copyWith(
                            color: AppColors.textInverse,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRadialExpansionOverlay() {
    return AnimatedBuilder(
      animation: _radialExpansionAnimation,
      builder: (context, child) {
        final screenSize = MediaQuery.of(context).size;
        final maxRadius = math.sqrt(
          math.pow(screenSize.width, 2) + math.pow(screenSize.height, 2),
        );

        return SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: CustomPaint(
            painter: RadialExpansionPainter(
              progress: _radialExpansionAnimation.value,
              maxRadius: maxRadius,
              color: AppColors.accentGreen,
            ),
          ),
        );
      },
    );
  }

  Widget _buildBurstOverlay() {
    return AnimatedBuilder(
      animation: _burstAnimation,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          child: CustomPaint(
            painter: BurstPainter(
              progress: _burstAnimation.value,
              color: AppColors.accentGreen,
            ),
          ),
        );
      },
    );
  }
}

// Custom painter for radial expansion animation
class RadialExpansionPainter extends CustomPainter {
  final double progress;
  final double maxRadius;
  final Color color;

  RadialExpansionPainter({
    required this.progress,
    required this.maxRadius,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center =
        Offset(size.width / 2, size.height - 90); // Start from button area
    final radius = progress * maxRadius;

    final paint = Paint()
      ..color = color.withValues(alpha: 0.8 * (1 - progress * 0.3))
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, paint);
  }

  @override
  bool shouldRepaint(RadialExpansionPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

// Custom painter for burst animation
class BurstPainter extends CustomPainter {
  final double progress;
  final Color color;

  BurstPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final paint = Paint()
      ..color = color.withValues(alpha: 0.6 * (1 - progress))
      ..style = PaintingStyle.fill;

    // Draw multiple expanding circles for burst effect
    for (int i = 0; i < 5; i++) {
      final radius = progress * (100 + i * 30);
      final alpha = (1 - progress) * (1 - i * 0.2);
      paint.color = color.withValues(alpha: alpha.clamp(0.0, 1.0));
      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(BurstPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
