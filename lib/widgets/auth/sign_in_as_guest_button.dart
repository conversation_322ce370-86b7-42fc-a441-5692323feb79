import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:unstack/theme/theme.dart';

Widget buildGetStartedButton(
  String text, {
  required VoidCallback onPressed,
}) {
  return Builder(
    builder: (context) => InkWell(
      borderRadius: BorderRadius.all(Radius.circular(28)),
      onTap: onPressed,
      child: Container(
        width: double.infinity,
        height: 80,
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(26),
          borderRadius: const BorderRadius.all(
            Radius.circular(48),
          ),
          border: Border.all(
            width: 2,
            color: Colors.white.withAlpha(51),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(51),
              blurRadius: 15,
              spreadRadius: -5,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              CupertinoIcons.bolt_horizontal_fill,
              size: 24,
              color: AppColors.whiteColor,
            ),
            const SizedBox(
              width: 12,
            ),
            Text(
              text,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.whiteColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
